// commission_records_view.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import '../../../../../core/base/common_refresher.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_details_entity.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/common_tabbar.dart';
import '../../../../../shared/widgets/common_table.dart';
import '../../../../routers/navigator_utils.dart';
import 'commission_records_cubit.dart';

// View
class CommissionRecordsView extends StatelessWidget {
  const CommissionRecordsView({super.key});

  @override
  Widget build(BuildContext context) => BlocProvider(
        create: (_) => CommissionRecordsCubit()..fetchCommissionRecords(),
        child: const _CommissionRecordsContent(),
      );
}

class _CommissionRecordsContent extends StatefulWidget {
  const _CommissionRecordsContent();

  @override
  State<_CommissionRecordsContent> createState() => _CommissionRecordsContentState();
}

class _CommissionRecordsContentState extends State<_CommissionRecordsContent> with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: DayType.values.length, vsync: this)..addListener(_onTabChanged);
  }

  void _onTabChanged() {
    context.read<CommissionRecordsCubit>().resetCommissionDetailsEntity();
    if (_tabController.indexIsChanging) return;
    if (_tabController.index != _tabController.previousIndex) {
      context.read<CommissionRecordsCubit>().changeDateType(_tabController.index);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      );

  PreferredSizeWidget _buildAppBar() => AppBar(
        leading: InkWell(
          onTap: () {
            sl<NavigatorService>().unFocus();
            sl<NavigatorService>().pop();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 14.gw),
            child: Image(
              image: const AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
              height: 20.gw,
              width: 20.gw,
            ),
          ),
        ),
        centerTitle: true,
        title: BlocSelector<CommissionRecordsCubit, CommissionRecordsState, TeamType>(
          selector: (state) => state.paymentTabIndex,
          builder: (context, state) => CommonTabBar(
            [
              CommonTabBarItem(title: '投注'),
              CommonTabBarItem(title: '充值'),
            ],
            currentIndex: state.index,
            onTap: (index) => context.read<CommissionRecordsCubit>().changePaymentTabIndex(index),
            style: CommonTabBarStyle.secondary,
            isScrollable: false,
          ),
        ),
        actions: [],
      );

  Widget _buildBody() => Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 12.gw,
        ),
        child: Column(
          children: [
            CommonTabBar(
              _buildTabItems(),
              currentIndex: _tabController.index,
              onTap: (index) => _tabController.animateTo(index),
              style: CommonTabBarStyle.secondary,
            ),
            SizedBox(height: 10.gw),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: List.generate(
                  3,
                  (_) => CommissionRecordsContent(),
                ),
              ),
            ),
          ],
        ),
      );

  /// Builds tab items for CommonTabBar
  List<CommonTabBarItem> _buildTabItems() => [
        CommonTabBarItem(title: 'yesterday'.tr()),
        CommonTabBarItem(title: 'this_month'.tr()),
        CommonTabBarItem(title: 'all_time'.tr()),
      ];
}

// Content Widget
class CommissionRecordsContent extends StatelessWidget {
  CommissionRecordsContent({super.key});
  final TextEditingController _controller = TextEditingController();
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  Widget build(BuildContext context) => BlocBuilder<CommissionRecordsCubit, CommissionRecordsState>(
        builder: (context, state) => Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            if (state.commissionDetailsNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.commissionDetailsNetState == NetState.dataSuccessState)
              Expanded(
                child: _CommissionTable(
                  records: state.commissionDetailsEntity?.records ?? [],
                  paymentType: state.paymentTabIndex,
                  refreshController: refreshController,
                ),
              ),
          ],
        ),
      );

  /// Builds search field based on Figma design
  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: const Color(0xFF101010),
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 14.gw,
                  color: const Color(0xFF636363),
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: TextStyle(
                      fontSize: 14.gw,
                      color: const Color(0xFF636363),
                    ),
                    decoration: InputDecoration(
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: TextStyle(
                        fontSize: 14.gw,
                        color: const Color(0xFF636363),
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gw),
            decoration: BoxDecoration(
              color: const Color(0xFFFFD038),
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: const Color(0xFFFFE258),
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () =>
                  context.read<CommissionRecordsCubit>().fetchCommissionRecords(childUserId: _controller.text.trim()),
              child: Text(
                'search'.tr(),
                style: TextStyle(
                  fontSize: 14.gw,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF030303),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Commission Table
class _CommissionTable extends StatelessWidget {
  final List<CommissionDetailsRecords> records;
  final TeamType paymentType;
  final RefreshController refreshController;

  const _CommissionTable({
    required this.records,
    required this.paymentType,
    required this.refreshController,
  });

  void _onRefresh(BuildContext context) {
    context.read<CommissionRecordsCubit>().updatePageNo(1);
    context.read<CommissionRecordsCubit>().fetchCommissionRecords();
    refreshController.resetNoData();
    refreshController.refreshCompleted();
  }

  void _onLoading(BuildContext context) async {
    final hasMore = await context.read<CommissionRecordsCubit>().loadMoreCommissionRecords();
    if (hasMore) {
      refreshController.loadComplete();
    } else {
      refreshController.loadNoData();
    }
  }

  List<CommonTableColumn> _getColumns() {
    if (paymentType == TeamType.bet) {
      return [
        CommonTableColumn(
          title: 'sub_agent'.tr(),
          key: 'subUserNo',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'bet_amount'.tr(),
          key: 'amount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'bet_comm'.tr(),
          key: 'commissionAmount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'date'.tr(),
          key: 'belongDate',
          flex: 2,
          style: CommonTableColumnStyle.yellowText, // Yellow text for last column
        ),
      ];
    } else {
      return [
        CommonTableColumn(
          title: 'sub_agent'.tr(),
          key: 'subUserNo',
          flex: 3,
        ),
        CommonTableColumn(
          title: 'recharge_amount'.tr(),
          key: 'amount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'recharge_commission_amount'.tr(),
          key: 'commissionAmount',
          flex: 2,
        ),
        CommonTableColumn(
          title: 'date'.tr(),
          key: 'belongDate',
          flex: 3,
          style: CommonTableColumnStyle.yellowText, // Yellow text for last column
        ),
      ];
    }
  }

  List<List<String>> _getTableData() {
    return records
        .map((record) => [
              record.subUserNo?.maskString ?? '',
              record.amount?.formattedMoney ?? '',
              record.commissionAmount?.formattedMoney ?? '',
              _formatDate(record.belongDate),
            ])
        .toList();
  }

  String _formatDate(String? date) {
    if (date == null) return '';
    try {
      return DateTime.parse(date).toIso8601String().split('T').first;
    } catch (e) {
      return date;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: CommonRefresher(
        onRefresh: () => _onRefresh(context),
        onLoading: () => _onLoading(context),
        refreshController: refreshController,
        enablePullDown: true,
        enablePullUp: true,
        listWidget: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: CommonTable(
              columns: _getColumns(),
              data: _getTableData(),
            ),
          ),
        ),
      ),
    );
  }
}
