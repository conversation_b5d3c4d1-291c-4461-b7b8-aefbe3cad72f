
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/utils/extentions.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/4_mine/promotion_rewards/team_management/team_management_cubit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/base/common_refresher.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/team_details_entity.dart';
import '../../../../../core/models/entities/team_members_entity.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/promotion/search_field.dart';
import '../../../../../shared/widgets/promotion/tab_bar_promotion.dart';
import '../../../../routers/navigator_utils.dart';

class TeamManagementView extends StatefulWidget {
  const TeamManagementView({super.key});

  @override
  State<TeamManagementView> createState() => _TeamManagementViewState();
}

class _TeamManagementViewState extends State<TeamManagementView> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    context.read<TeamManagementCubit>().fetchTeamMemberInfo();
    _tabController.addListener(() {
      context.read<TeamManagementCubit>().resetTeamData();
      if (_tabController.indexIsChanging) return;
      if (_tabController.index != _tabController.previousIndex) {
        context.read<TeamManagementCubit>().updatePageNo(1);
        switch (_tabController.index) {
          case 0:
            context.read<TeamManagementCubit>().fetchTeamMemberInfo();
            break;
          case 1:
            context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.bet); //bet
            break;
          case 2:
            context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.recharge); //recharge
            break;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('team_management'.tr()),
        leading: InkWell(
          onTap: () {
            sl<NavigatorService>().unFocus();
            sl<NavigatorService>().pop();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 14.gw),
            child: Image(
              image: const AssetImage("assets/images/toolBar/icon_toolBar_back.png"),
              height: 20.gw,
              width: 20.gw,
            ),
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.gw),
        child: TabBarPromotion(
          tabController: _tabController,
          tabs: [
            Tab(text: 'team_member_count'.tr()),
            Tab(text: 'team_betting'.tr()),
            Tab(text: 'team_profit_loss'.tr()),
          ],
          children: [
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  TeamMember(),
                  TeamDetailsBet(),
                  TeamDetailsRecharge(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class TeamMember extends StatelessWidget {
  TeamMember({super.key});

  final TextEditingController _controller = TextEditingController();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchTeamMemberInfo();
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreTeamMemberInfo();
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            SearchField(
              controller: _controller,
              hintText: 'enter_agent_id'.tr(),
              onSearch: () => context.read<TeamManagementCubit>().fetchTeamMemberInfo(childUserId: _controller.text),
            ),
            SizedBox(height: 14.gw),
            TabHeader(children: [
              TableHeaderCell(
                  text: '${'subordinate_count'.tr()}(${state.teamMembersEntity?.total ?? 0})', flex: 3, isBold: true),
              TableHeaderCell(text: 'levels'.tr(), flex: 2, isBold: true),
              TableHeaderCell(text: 'levels_time'.tr(), flex: 4, isBold: true),
            ]),
            if (state.teamMembersNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamMembersNetState == NetState.dataSuccessState)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: ListView.builder(
                      itemCount: state.teamMembersEntity?.records?.length ?? 0,
                      itemBuilder: (context, index) {
                        final record = state.teamMembersEntity?.records?[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          child: ScaleAnimation(
                            child: _TableBodyTeamMember(record: record ?? TeamMembersRecords()),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              )
          ],
        );
      },
    );
  }
}

class TeamDetailsBet extends StatelessWidget {
  TeamDetailsBet({super.key});

  final TextEditingController _controller = TextEditingController();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.bet);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreMyTeamDetail(type: TeamType.bet);
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            SearchField(
              controller: _controller,
              hintText: 'enter_agent_id'.tr(),
              onSearch: () => context
                  .read<TeamManagementCubit>()
                  .fetchMyTeamDetail(childUserId: _controller.text, type: TeamType.bet),
            ),
            SizedBox(height: 14.gw),
            TabHeader(children: [
              TableHeaderCell(
                  text: '${'subordinate_count'.tr()}(${state.teamDetailsEntity?.total ?? 0})', flex: 3, isBold: true),
              TableHeaderCell(text: 'bet_amount'.tr(), flex: 4, isBold: true),
              TableHeaderCell(text: 'contribution_commission'.tr(), flex: 2, isBold: true),
            ]),
            if (state.teamDetailsNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamDetailsNetState == NetState.dataSuccessState)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: ListView.builder(
                      itemCount: state.teamDetailsEntity?.records?.length ?? 0,
                      itemBuilder: (context, index) {
                        final record = state.teamDetailsEntity?.records?[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          child: ScaleAnimation(
                            child: _TableBodyTeamDetailsBet(record: record ?? TeamDetailsRecords()),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              )
          ],
        );
      },
    );
  }
}

class TeamDetailsRecharge extends StatelessWidget {
  TeamDetailsRecharge({super.key});

  final TextEditingController _controller = TextEditingController();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.recharge);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreMyTeamDetail(type: TeamType.recharge);
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            SearchField(
              controller: _controller,
              hintText: 'enter_agent_id'.tr(),
              onSearch: () => context
                  .read<TeamManagementCubit>()
                  .fetchMyTeamDetail(childUserId: _controller.text, type: TeamType.recharge),
            ),
            SizedBox(height: 14.gw),
            TabHeader(width: 75.gw, children: [
              TableHeaderCell(
                  text: '${'subordinate_count'.tr()}(${state.teamDetailsEntity?.total ?? 0})', flex: 5, isBold: true),
              TableHeaderCell(text: 'withdrawal_amount'.tr(), flex: 6, isBold: true),
              TableHeaderCell(text: 'recharge_amount'.tr(), flex: 6, isBold: true),
              TableHeaderCell(text: 'profit_loss_amount'.tr(), flex: 6, isBold: true),
              TableHeaderCell(text: 'contribution_commission'.tr(), flex: 5, isBold: true),
            ]),
            if (state.teamDetailsNetState == NetState.emptyDataState)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamDetailsNetState == NetState.dataSuccessState)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: ListView.builder(
                      itemCount: state.teamDetailsEntity?.records?.length ?? 0,
                      itemBuilder: (context, index) {
                        final record = state.teamDetailsEntity?.records?[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          child: ScaleAnimation(
                            child: _TableBodyTeamDetailsRecharge(record: record ?? TeamDetailsRecords()),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class _TableBodyTeamMember extends StatelessWidget {
  final TeamMembersRecords record;

  const _TableBodyTeamMember({required this.record});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 34.gw,
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Padding(
              padding: EdgeInsets.only(left: 24.gw),
              child: Text(
                record.subUserNo?.maskString() ?? '',
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 14.fs,
                  color: const Color(0xFF6A7391),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              height: 20.gw,
              width: 44.gw,
              margin: EdgeInsets.symmetric(horizontal: 12.gw),
              padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 2.gw),
              decoration: BoxDecoration(
                color: _getLevelColor(record.level ?? 0),
                borderRadius: BorderRadius.circular(10.gw),
              ),
              child: Text(
                _getLevelText(record.level ?? 0),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 12.fs,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              record.registerDate ?? '',
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF6A7391),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getLevelColor(int level) {
    switch (level) {
      case 1:
        return const Color(0XFFFF9500);
      case 2:
        return const Color(0xFFA89E65);
      case 3:
        return const Color(0xFFB5B5B5);
      default:
        return const Color(0xFFD4B88C);
    }
  }

  String _getLevelText(int level) {
    switch (level) {
      case 1:
        return '直属';
      case 2:
        return '2级';
      case 3:
        return '3级';
      case 4:
        return '4级';
      case 5:
        return '5级';
      default:
        return '直属';
    }
  }
}

class _TableBodyTeamDetailsBet extends StatelessWidget {
  const _TableBodyTeamDetailsBet({
    required this.record,
  });

  final TeamDetailsRecords record;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 34.gw,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 3,
            child: Padding(
              padding: EdgeInsets.only(left: 24.gw),
              child: Text(
                record.subUserNo?.maskString() ?? '',
                style: TextStyle(
                  fontSize: 14.fs,
                  color: const Color(0xFF6A7391),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              record.amount?.formattedMoney ?? '',
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF6A7391),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              record.commissionAmount?.formattedMoney ?? '',
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF6A7391),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TableBodyTeamDetailsRecharge extends StatelessWidget {
  const _TableBodyTeamDetailsRecharge({
    required this.record,
  });

  final TeamDetailsRecords record;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 34.gw,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 5,
            child: Padding(
              padding: EdgeInsets.only(left: 10.gw),
              child: Text(
                record.subUserNo?.maskString() ?? '',
                maxLines: 1,
                style: TextStyle(
                  fontSize: 14.fs,
                  color: const Color(0xFF6A7391),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Text(
              record.withdrawAmount?.formattedMoney ?? '',
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF6A7391),
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Text(
              record.amount?.formattedMoney ?? '',
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF6A7391),
              ),
            ),
          ),
          Expanded(
            flex: 6,
            child: Text(
              record.profitAmount?.formattedMoney ?? '',
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF6A7391),
              ),
            ),
          ),
          Expanded(
            flex: 5,
            child: Text(
              record.commissionAmount?.formattedMoney ?? '',
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF6A7391),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
