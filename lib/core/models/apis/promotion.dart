import 'package:wd/core/models/entities/commission_overview_entity.dart';

import '../../utils/http/https.dart';
import '../entities/commission_bet_entity.dart';
import '../entities/commission_details_entity.dart';
import '../entities/commission_recharge_entity.dart';
import '../entities/my_team_entity.dart';
import '../entities/team_details_entity.dart';
import '../entities/team_entity.dart';
import '../entities/team_members_entity.dart';

enum TeamType {
  bet, //0
  recharge, //1
}

enum DayType {
  yesterday, // 1
  thisMonth, // 2
  allTime, // 3
}

class PromotionApi {
  /// fetch team level
  static Future<TeamEntity?> fetchTeam() async {
    ResponseModel response = await Http().request<TeamEntity>(
      ApiConstants.team,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch my team info
  static Future<MyTeamEntity?> fetchMyTeam() async {
    ResponseModel response = await Http().request<MyTeamEntity>(
      ApiConstants.myTeam,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch commission info
  static Future<CommissionOverviewEntity?> fetchCommission() async {
    ResponseModel response = await Http().request<CommissionOverviewEntity>(
      ApiConstants.commission,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch team member info
  static Future<TeamMembersEntity?> fetchTeamMemberInfo({
    required int pageNo,
    String? childUserId,
  }) async {
    final params = {
      "pageNo": pageNo,
      "pageSize": 20,
      "childUserId": childUserId,
    };
    final sample = TeamMembersEntity()
      ..records = [
        TeamMembersRecords()
          ..subUserNo = 'U123456'
          ..level = 1
          ..registerDate = '2024-01-01',
        TeamMembersRecords()
          ..subUserNo = 'U654321'
          ..level = 2
          ..registerDate = '2024-02-01',
        TeamMembersRecords()
          ..subUserNo = 'U789012'
          ..level = 3
          ..registerDate = '2024-03-01',
        TeamMembersRecords()
          ..subUserNo = 'U210987'
          ..level = 1
          ..registerDate = '2024-04-01',
      ]
      ..total = 4
      ..size = 20
      ..current = pageNo
      ..orders = []
      ..optimizeCountSql = null
      ..searchCount = null
      ..optimizeJoinOfCountSql = null
      ..maxLimit = null
      ..countId = null
      ..pages = 1;
    return sample;
    ResponseModel response = await Http().request<TeamMembersEntity>(
      ApiConstants.myTeamMembers,
      params: _removeNullValues(params),
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch my team detail info
  static Future<TeamDetailsEntity?> fetchMyTeamDetail({
    required int pageNo,
    required TeamType type,
    String? childUserId,
  }) async {
    final params = {
      "pageNo": pageNo,
      "pageSize": 20,
      "childUserId": childUserId,
      "type": type.index,
    };
    final sample = TeamDetailsEntity()
      ..records = [
        TeamDetailsRecords()
          ..subUserNo = 'U123456'
          ..amount = 100
          ..commissionAmount = 10
          ..withdrawAmount = 50
          ..profitAmount = 50,
        TeamDetailsRecords()
          ..subUserNo = 'U654321'
          ..amount = 200
          ..commissionAmount = 20
          ..withdrawAmount = 100
          ..profitAmount = 100,
        TeamDetailsRecords()
          ..subUserNo = 'U789012'
          ..amount = 300
          ..commissionAmount = 30
          ..withdrawAmount = 150
          ..profitAmount = 150,
        TeamDetailsRecords()
          ..subUserNo = 'U210987'
          ..amount = 400
          ..commissionAmount = 40
          ..withdrawAmount = 200
          ..profitAmount = 200,
      ]
      ..total = 4
      ..size = 20
      ..current = pageNo
      ..orders = []
      ..optimizeCountSql = null
      ..searchCount = null
      ..optimizeJoinOfCountSql = null
      ..maxLimit = null
      ..countId = null
      ..pages = 1;
    return sample;
    ResponseModel response = await Http().request<TeamDetailsEntity>(
      ApiConstants.myTeamDetail,
      params: _removeNullValues(params),
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch commission detail info
  static Future<CommissionDetailsEntity?> fetchCommissionDetail({
    required TeamType type,
    required DayType dateType,
    required int pageNo,
    String? childUserId,
  }) async {
    final params = {
      "type": type.index,
      "dateType": dateType.index + 1,
      "pageNo": pageNo,
      "pageSize": 20,
      "childUserId": childUserId,
    };
    ResponseModel response = await Http().request<CommissionDetailsEntity>(
      ApiConstants.commissionDetail,
      params: _removeNullValues(params),
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch commission plan
  static Future<CommissionRechargeEntity?> fetchCommissionPlan() async {
    ResponseModel response = await Http().request<CommissionRechargeEntity>(
      ApiConstants.promotionCashInConfig,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch promotion bet config
  static Future<CommissionBetEntity?> fetchPromotionBetConfig() async {
    ResponseModel response = await Http().request<CommissionBetEntity>(
      ApiConstants.promotionBetConfig,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  static Future<bool?> commissionReceive() async {
    ResponseModel response = await Http().request<bool>(
      ApiConstants.commissionReceive,
    );
    if (response.isSuccess) {
      return true;
    } else {
      return false;
    }
  }

  /// 获取用户邀请链接
  static Future<String?> fetchUserInviteLink() async {
    ResponseModel response = await Http().request<String>(
      ApiConstants.userInviteLink,
      needShowToast: false,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }
}

Map<String, dynamic> _removeNullValues(Map<String, dynamic> input) {
  return Map.fromEntries(input.entries.where((e) => e.value != null));
}
