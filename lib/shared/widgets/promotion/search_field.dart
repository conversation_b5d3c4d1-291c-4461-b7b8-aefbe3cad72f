import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

class SearchField extends StatelessWidget {
  final String hintText;
  final TextEditingController? controller;
  final VoidCallback? onSearch;
  final ValueChanged<String>? onChanged;
  final EdgeInsetsGeometry? margin;

  const SearchField({
    super.key,
    this.hintText = '',
    this.controller,
    this.onSearch,
    this.onChanged,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40.gw,
      margin: margin,
      decoration: BoxDecoration(
        color: const Color(0xFF000000).withOpacity(0.2),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.r,
            spreadRadius: 2.r,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              onChanged: onChanged,
              style: TextStyle(
                fontSize: 12.fs,
                color: Colors.white,
              ),
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(
                  fontSize: 12.fs,
                  color: Colors.white,
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 13.gw),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 7.gw),
            child: Container(
              width: 70.gw,
              height: 30.gw,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xffEACA9F),
                    Color(0xffB9936D),
                  ],
                ),
                borderRadius: BorderRadius.all(
                  Radius.circular(20.r),
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onSearch,
                  borderRadius: BorderRadius.horizontal(right: Radius.circular(20.r)),
                  child: Center(
                    child: Text(
                      '查询',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.fs,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
